# mobimed:App\Events\EntityWasUpdated

## Event Information

- **Event Type**: mobimed:App\Events\EntityWasUpdated
- **Model**: patient
- **Event ID**: 1777
- **Timestamp**: 2025-08-06T01:13:28.001Z
- **Webhook Event**: EntityWasUpdated
- **Webhook Model**: Patient

## Original Socket Event Data

```json
{
  "type": "patient",
  "payload": {
    "id": 1777,
    "createdAt": "2025-07-30T21:35:13.000Z",
    "updatedAt": "2025-08-05T22:00:10.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "AP",
    "lastName": "CC we",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1650-483486",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1826,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      12647,
      12649,
      12648,
      12650
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1777.png",
    "avatarUrl": null
  },
  "socket": null
}
```

## Converted Webhook Data

```json
{
  "event": "EntityWasUpdated",
  "model": "Patient",
  "id": 1777,
  "payload": {
    "id": 1777,
    "createdAt": "2025-07-30T21:35:13.000Z",
    "updatedAt": "2025-08-05T22:00:10.000Z",
    "createdBy": 5003,
    "updatedBy": 5003,
    "firstName": "AP",
    "lastName": "CC we",
    "dob": null,
    "ssn": null,
    "flashMessage": "",
    "active": true,
    "phoneMobile": "+880 1650-483486",
    "phonePersonal": null,
    "phoneBusiness": null,
    "email": "<EMAIL>",
    "title": null,
    "titleSuffix": null,
    "healthInsurance": null,
    "gender": null,
    "addresses": [
      {
        "id": 1826,
        "label": null,
        "name": null,
        "street": null,
        "streetNumber": null,
        "postalCode": null,
        "city": null,
        "country": "US",
        "primary": 1
      }
    ],
    "categories": [],
    "customFields": [
      12647,
      12649,
      12648,
      12650
    ],
    "invoices": [],
    "payments": [],
    "files": [],
    "history": [],
    "appointments": [],
    "messages": [],
    "medications": [],
    "personalWebForms": [],
    "qrUrl": "https://api-ccdemo.clinicore.eu/v1/qr/100/1777.png",
    "avatarUrl": null
  },
  "timestamp": "2025-08-06T01:13:28.001Z"
}
```

## Event Processing

This event was received from the CC socket server and converted to webhook format for processing by the DermaCare sync system.

### Processing Flow
1. **Socket Event Received**: mobimed:App\Events\EntityWasUpdated
2. **Data Extraction**: Extracted model type and payload data
3. **Webhook Conversion**: Converted to webhook format compatible with ccHandler.ts
4. **Webhook Transmission**: Sent to dev server at http://localhost:8787/webhooks/cc

---
*Generated by DermaCare Socket Event Logger*
