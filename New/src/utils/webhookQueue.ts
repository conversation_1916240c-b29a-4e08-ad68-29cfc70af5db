/**
 * Webhook Queue Utilities
 *
 * Provides utilities for managing webhook queue operations with duplicate prevention.
 * Implements atomic delete-then-insert operations to prevent race conditions and
 * ensure only the most recent webhook for each sourceId+type combination is processed.
 *
 * @fileoverview Webhook queue management utilities
 * @version 1.0.0
 * @since 2024-08-05
 */

import { dbSchema } from "@database";
import { and, eq, inArray, or } from "drizzle-orm";
import type { NeonHttpDatabase } from "drizzle-orm/neon-http";
import type { APContactCreationWebhookPayload } from "@/processors/apWebhook";
import type { CCWebhookPayload } from "@/processors/ccWebhook";
import { logDebug, logInfo, logWarn } from "@/utils/logger";

/**
 * Type for webhook queue insert operations
 */
type WebhookQueueInsert = typeof dbSchema.webhookQueue.$inferInsert;

/**
 * Type for webhook queue select operations
 */
type WebhookQueue = typeof dbSchema.webhookQueue.$inferSelect;

/**
 * Database type for the webhook queue operations
 */
type DrizzleDb = NeonHttpDatabase<typeof dbSchema>;

/**
 * Check for duplicate records before adding to queue
 *
 * Performs comprehensive duplicate prevention by checking:
 * 1. Local patient table for existing records matching the source ID
 * 2. Local appointment table for existing records matching the source ID
 * 3. Queue table for any existing records that match AP or CC IDs from found records
 * 4. Queue table for direct source ID matches
 *
 * @param db - Drizzle database instance
 * @param sourceId - Source ID from webhook payload
 * @param type - Webhook type (patient or appointment)
 * @returns Promise resolving to object with duplicate status and related IDs
 */
async function checkForDuplicates(
	db: DrizzleDb,
	sourceId: string,
	type: "patient" | "appointment",
): Promise<{ hasDuplicates: boolean; relatedIds: string[] }> {
	const relatedIds: string[] = [sourceId];

	try {
		if (type === "patient") {
			// Query patient table for existing records matching source ID
			const existingPatients = await db
				.select({
					apId: dbSchema.patient.apId,
					ccId: dbSchema.patient.ccId,
				})
				.from(dbSchema.patient)
				.where(
					or(
						eq(dbSchema.patient.apId, sourceId),
						eq(dbSchema.patient.ccId, parseInt(sourceId) || -1),
					),
				);

			// Extract AP and CC IDs from found patients
			for (const patient of existingPatients) {
				if (patient.apId) relatedIds.push(patient.apId);
				if (patient.ccId) relatedIds.push(patient.ccId.toString());
			}
		} else if (type === "appointment") {
			// Query appointment table for existing records matching source ID
			const existingAppointments = await db
				.select({
					apId: dbSchema.appointment.apId,
					ccId: dbSchema.appointment.ccId,
				})
				.from(dbSchema.appointment)
				.where(
					or(
						eq(dbSchema.appointment.apId, sourceId),
						eq(dbSchema.appointment.ccId, parseInt(sourceId) || -1),
					),
				);

			// Extract AP and CC IDs from found appointments
			for (const appointment of existingAppointments) {
				if (appointment.apId) relatedIds.push(appointment.apId);
				if (appointment.ccId) relatedIds.push(appointment.ccId.toString());
			}
		}

		// Check queue table for any existing records matching related IDs
		if (relatedIds.length > 1) {
			const existingQueueRecords = await db
				.select({ id: dbSchema.webhookQueue.id })
				.from(dbSchema.webhookQueue)
				.where(
					and(
						inArray(dbSchema.webhookQueue.sourceId, relatedIds),
						eq(dbSchema.webhookQueue.type, type),
					),
				)
				.limit(1);

			if (existingQueueRecords.length > 0) {
				logInfo(
					`Duplicate prevention: Found existing queue record for related IDs`,
					{
						sourceId,
						type,
						relatedIds,
						existingQueueRecordId: existingQueueRecords[0].id,
					},
				);
				return { hasDuplicates: true, relatedIds };
			}
		}

		return { hasDuplicates: false, relatedIds };
	} catch (error) {
		logWarn(
			`Error during duplicate check, allowing webhook to proceed: ${error}`,
			{
				sourceId,
				type,
				error: String(error),
			},
		);
		// Return false on error to allow processing (fail-safe)
		return { hasDuplicates: false, relatedIds };
	}
}

/**
 * Add webhook to queue with enhanced duplicate prevention
 *
 * This function implements duplicate prevention using non-transactional operations:
 * 1. Querying for existing pending webhooks with same sourceId and type
 * 2. Deleting any found pending webhooks (with error handling)
 * 3. Inserting the new webhook record
 * 4. Returning the created webhook
 *
 * This ensures that only the most recent webhook for each sourceId+type
 * combination is processed. While not fully atomic due to Neon HTTP driver
 * limitations, the approach is safe for webhook queue operations where:
 * - Newer webhooks should supersede older ones
 * - Processing logic is idempotent
 * - Temporary inconsistencies are acceptable
 *
 * @param db - Drizzle database instance
 * @param webhookData - Webhook data to insert
 * @returns Promise resolving to the created webhook record
 *
 * @throws {Error} When webhook insertion fails or database operation errors occur
 *
 * @example
 * ```typescript
 * const webhook = await addWebhookToQueue(db, {
 *   source: "cc",
 *   sourceId: "123",
 *   type: "patient",
 *   payload: ccWebhookPayload,
 *   status: "pending"
 * });
 * ```
 *
 * @since 1.0.0
 */
export async function addWebhookToQueue(
	db: DrizzleDb,
	webhookData: WebhookQueueInsert,
): Promise<WebhookQueue> {
	// Step 1: Enhanced duplicate prevention - check for related records
	const duplicateCheck = await checkForDuplicates(
		db,
		webhookData.sourceId,
		webhookData.type as "patient" | "appointment",
	);

	if (duplicateCheck.hasDuplicates) {
		// Find and delete existing PENDING queue records to prevent duplicate processing
		// Use the same relatedIds logic that was used for duplicate detection
		// Only move pending webhooks - completed/processing webhooks should remain
		const existingQueueRecords = await db
			.select({ id: dbSchema.webhookQueue.id })
			.from(dbSchema.webhookQueue)
			.where(
				and(
					inArray(dbSchema.webhookQueue.sourceId, duplicateCheck.relatedIds),
					eq(
						dbSchema.webhookQueue.type,
						webhookData.type as "patient" | "appointment",
					),
					eq(dbSchema.webhookQueue.status, "pending"),
				),
			);

		if (existingQueueRecords.length > 0) {
			try {
				await moveWebhookQueueRecordsToLogs(
					db,
					existingQueueRecords.map((r) => r.id),
					"duplicate_prevention",
					{
						sourceId: webhookData.sourceId,
						type: webhookData.type,
						source: webhookData.source,
						reason: "Enhanced duplicate prevention - related record exists",
					},
				);

				logInfo(
					`Duplicate prevention: Moved ${existingQueueRecords.length} existing queue record(s) to logs for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
					{
						movedWebhookIds: existingQueueRecords.map((r) => r.id),
						sourceId: webhookData.sourceId,
						type: webhookData.type,
						source: webhookData.source,
					},
				);
			} catch (deleteError) {
				logWarn(
					`Failed to move existing queue records to logs during duplicate prevention: ${deleteError}`,
					{
						sourceId: webhookData.sourceId,
						type: webhookData.type,
						source: webhookData.source,
						error: String(deleteError),
					},
				);
			}
		}

		// Skip adding new record due to duplicate prevention
		throw new Error(
			`Duplicate prevention: Skipping webhook addition for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type} - related record already exists or being processed`,
		);
	}

	// Step 2: Standard duplicate prevention - find existing pending webhooks with same sourceId and type
	const existingWebhooks = await db
		.select({ id: dbSchema.webhookQueue.id })
		.from(dbSchema.webhookQueue)
		.where(
			and(
				eq(dbSchema.webhookQueue.sourceId, webhookData.sourceId),
				eq(
					dbSchema.webhookQueue.type,
					webhookData.type as "patient" | "appointment",
				),
				eq(dbSchema.webhookQueue.status, "pending"),
			),
		);

	// Step 3: Move existing pending webhooks to logs if found
	if (existingWebhooks.length > 0) {
		const webhookIds = existingWebhooks.map((w) => w.id);

		try {
			await moveWebhookQueueRecordsToLogs(
				db,
				webhookIds,
				"duplicate_prevention",
				{
					sourceId: webhookData.sourceId,
					type: webhookData.type,
					source: webhookData.source,
					reason: "Standard duplicate prevention - newer webhook supersedes pending",
				},
			);

			logInfo(
				`Moved ${existingWebhooks.length} existing pending webhook(s) to logs for ${webhookData.source}:${webhookData.sourceId}:${webhookData.type}`,
				{
					movedWebhookIds: webhookIds,
					sourceId: webhookData.sourceId,
					type: webhookData.type,
					source: webhookData.source,
				},
			);
		} catch (deleteError) {
			// Log delete error but continue with insert - newer webhook is more important
			logInfo(
				`Failed to move existing pending webhooks to logs, continuing with insert: ${deleteError}`,
				{
					sourceId: webhookData.sourceId,
					type: webhookData.type,
					source: webhookData.source,
					error: String(deleteError),
				},
			);
		}
	}

	// Step 4: Insert the new webhook record (this must succeed)
	const [createdWebhook] = await db
		.insert(dbSchema.webhookQueue)
		.values(webhookData)
		.returning();

	logDebug(
		`Added new webhook to queue: ${createdWebhook.id} (${webhookData.source}:${webhookData.sourceId}:${webhookData.type})`,
		{
			webhookId: createdWebhook.id,
			sourceId: webhookData.sourceId,
			type: webhookData.type,
			source: webhookData.source,
			replacedExisting: existingWebhooks.length > 0,
		},
	);

	return createdWebhook;
}

/**
 * Extract source information from webhook payload
 *
 * Determines the source platform, sourceId, and type from webhook payload.
 * Used to normalize webhook data before queue insertion.
 *
 * @param payload - AP or CC webhook payload
 * @returns Object containing source, sourceId, and type information
 *
 * @throws {Error} When payload is invalid or missing required fields
 *
 * @example
 * ```typescript
 * const sourceInfo = extractSourceInfo(ccWebhookPayload);
 * // Returns: { source: "cc", sourceId: "123", type: "patient" }
 * ```
 *
 * @since 1.0.0
 */
export function extractSourceInfo(
	payload: APContactCreationWebhookPayload | CCWebhookPayload,
): {
	source: "ap" | "cc";
	sourceId: string;
	type: "patient" | "appointment";
} {
	// Check if it's an AP webhook payload
	if ("contact_id" in payload) {
		return {
			source: "ap",
			sourceId: payload.contact_id,
			type: payload.calendar ? "appointment" : "patient",
		};
	}

	// Check if it's a CC webhook payload
	if ("id" in payload) {
		return {
			source: "cc",
			sourceId: payload.id.toString(),
			type:
				payload.model?.toLowerCase() === "appointment"
					? "appointment"
					: "patient",
		};
	}

	throw new Error(
		"Invalid webhook payload: missing required identification fields",
	);
}

/**
 * Move webhook queue records to logs table instead of permanently deleting them
 *
 * This function preserves audit trail by copying webhook queue records to the
 * queueLogs table before deletion. This allows for historical tracking and
 * debugging while maintaining queue performance.
 *
 * @param db - Drizzle database instance
 * @param webhookIds - Array of webhook queue IDs to move to logs
 * @param deletionReason - Reason for deletion (completed, duplicate_prevention, failed, stuck)
 * @param deletionContext - Additional context about the deletion
 * @returns Promise resolving to the number of records moved
 *
 * @throws {Error} When database operations fail
 *
 * @example
 * ```typescript
 * await moveWebhookQueueRecordsToLogs(db, ["webhook-id-1"], "completed", {
 *   processingDuration: 5000,
 *   success: true
 * });
 * ```
 *
 * @since 1.0.0
 */
export async function moveWebhookQueueRecordsToLogs(
	db: DrizzleDb,
	webhookIds: string[],
	deletionReason: "completed" | "duplicate_prevention" | "failed" | "stuck",
	deletionContext?: Record<string, unknown>,
): Promise<number> {
	if (webhookIds.length === 0) {
		return 0;
	}

	try {
		// Step 1: Fetch the webhook records to be moved
		const webhookRecords = await db
			.select()
			.from(dbSchema.webhookQueue)
			.where(inArray(dbSchema.webhookQueue.id, webhookIds));

		if (webhookRecords.length === 0) {
			logWarn("No webhook records found to move to logs", {
				webhookIds,
				deletionReason,
			});
			return 0;
		}

		// Step 2: Insert records into queue logs table
		const logRecords = webhookRecords.map((record) => ({
			originalId: record.id,
			source: record.source,
			sourceId: record.sourceId,
			type: record.type,
			payload: record.payload,
			status: record.status,
			retryCount: record.retryCount,
			lastRetryAttemptAt: record.lastRetryAttemptAt,
			lastRetryReason: record.lastRetryReason,
			maxRetries: record.maxRetries,
			processingStartedAt: record.processingStartedAt,
			processingCompletedAt: record.processingCompletedAt,
			errorMessage: record.errorMessage,
			errorDetails: record.errorDetails,
			originalCreatedAt: record.createdAt,
			originalUpdatedAt: record.updatedAt,
			deletionReason,
			deletionContext,
		}));

		await db.insert(dbSchema.queueLogs).values(logRecords);

		// Step 3: Delete records from webhook queue
		await db
			.delete(dbSchema.webhookQueue)
			.where(inArray(dbSchema.webhookQueue.id, webhookIds));

		logInfo(
			`Moved ${webhookRecords.length} webhook queue record(s) to logs`,
			{
				webhookIds,
				deletionReason,
				movedCount: webhookRecords.length,
			},
		);

		return webhookRecords.length;
	} catch (error) {
		logWarn(
			`Failed to move webhook queue records to logs: ${error}`,
			{
				webhookIds,
				deletionReason,
				error: String(error),
			},
		);
		throw new Error(`Failed to move webhook queue records to logs: ${error}`);
	}
}
