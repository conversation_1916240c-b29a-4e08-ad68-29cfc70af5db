CREATE TABLE "queue_logs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"original_id" varchar(255) NOT NULL,
	"source" text NOT NULL,
	"source_id" text NOT NULL,
	"type" varchar(255) DEFAULT 'patient' NOT NULL,
	"payload" jsonb NOT NULL,
	"status" varchar(255) DEFAULT 'pending' NOT NULL,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"last_retry_attempt_at" timestamp,
	"last_retry_reason" text,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"processing_started_at" timestamp,
	"processing_completed_at" timestamp,
	"error_message" text,
	"error_details" jsonb,
	"original_created_at" timestamp NOT NULL,
	"original_updated_at" timestamp NOT NULL,
	"deleted_at" timestamp DEFAULT now() NOT NULL,
	"deletion_reason" varchar(255) NOT NULL,
	"deletion_context" jsonb
);
